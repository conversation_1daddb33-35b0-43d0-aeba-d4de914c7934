import { NextPage } from 'next'
import Head from 'next/head'
import { useEffect, useState } from 'react'

interface UserProgress {
  totalAnswers: number
  averageRating: number | null
}

interface HistoryEntry {
  questionPrompt: string
  rating: number | null
  answeredAt: string
}

const Dashboard: NextPage = () => {
  const [progress, setProgress] = useState<UserProgress | null>(null)
  const [history, setHistory] = useState<HistoryEntry[]>([])

  useEffect(() => {
    fetch('/graphql', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        query: `query Dashboard($userId: ID!) {\n  userProgress(userId: $userId) { totalAnswers averageRating }\n  userAnswerHistory(userId: $userId) { questionPrompt rating answeredAt }\n}`,
        variables: { userId: '00000000-0000-0000-0000-000000000001' },
      }),
    })
      .then((res) => res.json())
      .then((res) => {
        setProgress(res.data.userProgress)
        setHistory(res.data.userAnswerHistory)
      })
  }, [])

  return (
    <div>
      <Head>
        <title>Dashboard</title>
      </Head>
      <h1>User Dashboard</h1>
      {progress && (
        <div>
          <p>Total Answers: {progress.totalAnswers}</p>
          <p>
            Average Rating:{' '}
            {progress.averageRating !== null
              ? progress.averageRating.toFixed(2)
              : 'N/A'}
          </p>
        </div>
      )}
      <h2>Answer History</h2>
      <ul>
        {history.map((h, i) => (
          <li key={i}>
            <strong>{h.questionPrompt}</strong> - Rating:{' '}
            {h.rating ?? 'N/A'} - {new Date(h.answeredAt).toLocaleString()}
          </li>
        ))}
      </ul>
    </div>
  )
}

export default Dashboard
