import { NextPage } from 'next'
import Head from 'next/head'
import { useState } from 'react'

const Admin: NextPage = () => {
  const [prompt, setPrompt] = useState('')
  const [topic, setTopic] = useState('')
  const [example, setExample] = useState('')
  const [status, setStatus] = useState('')

  const submit = async () => {
    const res = await fetch('/graphql', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        query: `mutation Create($prompt: String!, $topic: String!, $exampleAnswer: String) {\n  createQuestion(prompt: $prompt, topic: $topic, exampleAnswer: $exampleAnswer) { id }\n}`,
        variables: { prompt, topic, exampleAnswer: example || null },
      }),
    })
    const data = await res.json()
    if (data.errors) {
      setStatus('Error creating question')
    } else {
      setStatus('Question created!')
      setPrompt('')
      setTopic('')
      setExample('')
    }
  }

  return (
    <div>
      <Head>
        <title>Admin - Create Question</title>
      </Head>
      <h1>Create Question</h1>
      <div>
        <label>Prompt:</label>
        <br />
        <textarea value={prompt} onChange={(e) => setPrompt(e.target.value)} rows={4} cols={50} />
      </div>
      <div>
        <label>Topic:</label>
        <br />
        <input type="text" value={topic} onChange={(e) => setTopic(e.target.value)} />
      </div>
      <div>
        <label>Example Answer:</label>
        <br />
        <textarea value={example} onChange={(e) => setExample(e.target.value)} rows={3} cols={50} />
      </div>
      <button onClick={submit}>Create</button>
      {status && <p>{status}</p>}
    </div>
  )
}

export default Admin
