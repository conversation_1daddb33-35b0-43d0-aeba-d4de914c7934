import { NextPage } from 'next'
import Head from 'next/head'
import { useEffect, useState } from 'react'
import CountdownTimer from '../components/CountdownTimer'
import StarRating from '../components/StarRating'
import AudioRecorder from '../components/AudioRecorder'

interface Question {
  id: string
  prompt: string
  topic: string
  exampleAnswer?: string
}

const Practice: NextPage = () => {
  const [questions, setQuestions] = useState<Question[]>([])
  const [current, setCurrent] = useState(0)
  const [answer, setAnswer] = useState('')
  const [rating, setRating] = useState(0)
  const [audioUrl, setAudioUrl] = useState('')
  const [timerKey, setTimerKey] = useState(0)
  const [timeLeft, setTimeLeft] = useState(60)

  useEffect(() => {
    fetch('/graphql', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query: '{ getPracticeQuestions { id prompt topic exampleAnswer } }' }),
    })
      .then((res) => res.json())
      .then((res) => setQuestions(res.data.getPracticeQuestions))
  }, [])

  const submitAnswer = async () => {
    const q = questions[current]
    if (!q) return

    await fetch('/graphql', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        query: `mutation Submit($questionId: ID!, $userId: ID!, $answerText: String!, $rating: Int, $timeTakenSeconds: Int, $audioUrl: String) {\n          submitAnswer(questionId: $questionId, userId: $userId, answerText: $answerText, rating: $rating, timeTakenSeconds: $timeTakenSeconds, audioUrl: $audioUrl) { id }\n        }`,
        variables: {
          questionId: q.id,
          userId: '00000000-0000-0000-0000-000000000001',
          answerText: answer,
          rating: rating,
          timeTakenSeconds: 60 - timeLeft,
          audioUrl: audioUrl || null,
        },
      }),
    })

    setAnswer('')
    setRating(0)
    setAudioUrl('')
    setTimeLeft(60)
    setTimerKey(timerKey + 1)
    setCurrent(current + 1)
  }

  const handleTimerTick = (sec: number) => {
    setTimeLeft(Math.max(sec, 0))
  }

  const handleTimerComplete = () => {
    submitAnswer()
  }

  const q = questions[current]

  return (
    <div>
      <Head>
        <title>Practice</title>
      </Head>
      <h1>Practice Questions</h1>
      {q ? (
        <div>
          <p>{q.prompt}</p>
          <CountdownTimer key={timerKey} initialSeconds={60} onTick={handleTimerTick} onComplete={handleTimerComplete} />
          <div>
          <textarea value={answer} onChange={(e) => setAnswer(e.target.value)} rows={4} cols={50} />
          </div>
          <AudioRecorder onUploaded={setAudioUrl} />
          <StarRating value={rating} onChange={setRating} />
          <button onClick={submitAnswer}>Submit Answer</button>
        </div>
      ) : (
        <p>No more questions.</p>
      )}
    </div>
  )
}

export default Practice
