import React, { useEffect, useState } from 'react'

interface CountdownTimerProps {
  initialSeconds: number
  onComplete?: () => void
  onTick?: (secondsLeft: number) => void
}

const CountdownTimer: React.FC<CountdownTimerProps> = ({ initialSeconds, onComplete, onTick }) => {
  const [secondsLeft, setSecondsLeft] = useState(initialSeconds)

  useEffect(() => {
    setSecondsLeft(initialSeconds)
    const interval = setInterval(() => {
      setSecondsLeft((prev) => {
        const next = prev - 1
        if (onTick) onTick(next)
        return next
      })
    }, 1000)
    return () => clearInterval(interval)
  }, [initialSeconds, onTick])

  useEffect(() => {
    if (secondsLeft <= 0) {
      onComplete && onComplete()
    }
  }, [secondsLeft, onComplete])

  return <span>{secondsLeft}s</span>
}

export default CountdownTimer
