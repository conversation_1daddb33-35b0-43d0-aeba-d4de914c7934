import { render, screen } from '@testing-library/react'
import { act } from 'react-dom/test-utils'
import CountdownTimer from '../CountdownTimer'

test('counts down and triggers callbacks', () => {
  jest.useFakeTimers()
  const onTick = jest.fn()
  const onComplete = jest.fn()

  render(<CountdownTimer initialSeconds={2} onTick={onTick} onComplete={onComplete} />)
  expect(screen.getByText('2s')).toBeInTheDocument()

  act(() => {
    jest.advanceTimersByTime(1000)
  })
  expect(screen.getByText('1s')).toBeInTheDocument()
  expect(onTick).toHaveBeenCalledWith(1)

  act(() => {
    jest.advanceTimersByTime(1000)
  })
  expect(onComplete).toHaveBeenCalled()
  jest.useRealTimers()
})
