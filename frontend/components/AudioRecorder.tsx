import React, { useRef, useState } from 'react'

interface AudioRecorderProps {
  onUploaded: (url: string) => void
}

const AudioRecorder: React.FC<AudioRecorderProps> = ({ onUploaded }) => {
  const [recording, setRecording] = useState(false)
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const chunks = useRef<BlobPart[]>([])

  const startRecording = async () => {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
    const recorder = new MediaRecorder(stream)
    mediaRecorderRef.current = recorder
    chunks.current = []
    recorder.ondataavailable = (e) => chunks.current.push(e.data)
    recorder.onstop = async () => {
      const blob = new Blob(chunks.current, { type: 'audio/webm' })
      const formData = new FormData()
      formData.append('file', blob, 'recording.webm')
      const res = await fetch('/api/upload-audio', {
        method: 'POST',
        body: formData,
      })
      const data = await res.json()
      onUploaded(data.url)
    }
    recorder.start()
    setRecording(true)
  }

  const stopRecording = () => {
    mediaRecorderRef.current?.stop()
    setRecording(false)
  }

  return (
    <div>
      {recording ? (
        <button onClick={stopRecording}>Stop Recording</button>
      ) : (
        <button onClick={startRecording}>Record Answer</button>
      )}
    </div>
  )
}

export default AudioRecorder
