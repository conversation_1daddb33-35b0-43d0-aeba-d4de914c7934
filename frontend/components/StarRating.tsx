import React from 'react'

interface StarRatingProps {
  value: number
  max?: number
  onChange: (value: number) => void
}

const StarRating: React.FC<StarRatingProps> = ({ value, max = 5, onChange }) => {
  return (
    <div>
      {Array.from({ length: max }).map((_, i) => {
        const ratingValue = i + 1
        return (
          <span
            key={ratingValue}
            onClick={() => onChange(ratingValue)}
            style={{ cursor: 'pointer', fontSize: '1.5rem', color: ratingValue <= value ? '#ffc107' : '#e4e5e9' }}
            data-testid={`star-${ratingValue}`}
          >
            {ratingValue <= value ? '★' : '☆'}
          </span>
        )
      })}
    </div>
  )
}

export default StarRating
