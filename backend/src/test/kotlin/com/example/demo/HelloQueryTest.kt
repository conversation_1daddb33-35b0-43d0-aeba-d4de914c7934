package com.example.demo

import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.graphql.GraphQlTest
import org.springframework.graphql.test.tester.GraphQlTester

@GraphQlTest(HelloQuery::class)
class HelloQueryTest {

    @Autowired
    lateinit var graphQlTester: GraphQlTester

    @Test
    fun `hello returns greeting`() {
        graphQlTester.document("{ hello }")
            .execute()
            .path("hello")
            .entity(String::class.java)
            .isEqualTo("Hello, GraphQL!")
    }
}
