package com.example.demo

import org.junit.jupiter.api.Test
import org.mockito.BDDMockito.given
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.graphql.GraphQlTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.graphql.test.tester.GraphQlTester
import java.time.LocalDateTime
import java.util.UUID

@GraphQlTest(PracticeResolver::class)
class PracticeResolverTest {

    @Autowired
    lateinit var graphQlTester: GraphQlTester

    @MockBean
    lateinit var questionRepository: QuestionRepository

    @MockBean
    lateinit var userAnswerRepository: UserAnswerRepository

    @Test
    fun `getPracticeQuestions uses repository`() {
        val question = Question(
            id = UUID.randomUUID(),
            prompt = "Prompt",
            topic = "Topic",
            exampleAnswer = "Example",
            createdAt = LocalDateTime.now()
        )
        given(questionRepository.findPracticeQuestions()).willReturn(listOf(question))

        graphQlTester.document("{ getPracticeQuestions { id prompt } }")
            .execute()
            .path("getPracticeQuestions[0].id")
            .entity(UUID::class.java)
            .isEqualTo(question.id)
    }
}
