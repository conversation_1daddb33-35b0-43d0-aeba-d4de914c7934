package com.example.demo

import org.junit.jupiter.api.Test
import org.mockito.BDDMockito.given
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.graphql.GraphQlTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.graphql.test.tester.GraphQlTester
import java.time.LocalDateTime
import java.util.UUID

@GraphQlTest(QuestionQuery::class)
class QuestionQueryTest {

    @Autowired
    lateinit var graphQlTester: GraphQlTester

    @MockBean
    lateinit var repository: QuestionRepository

    @Test
    fun `questions returns data from repository`() {
        val question = Question(
            id = UUID.randomUUID(),
            prompt = "Prompt?",
            topic = "Topic",
            exampleAnswer = "Example",
            createdAt = LocalDateTime.now()
        )
        given(repository.findAll()).willReturn(listOf(question))

        graphQlTester.document("{ questions { id prompt topic } }")
            .execute()
            .path("questions[0].id").entity(UUID::class.java).isEqualTo(question.id)
            .path("questions[0].prompt").entity(String::class.java).isEqualTo("Prompt?")
            .path("questions[0].topic").entity(String::class.java).isEqualTo("Topic")
    }
}
