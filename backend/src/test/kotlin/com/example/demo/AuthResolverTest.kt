package com.example.demo

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.graphql.GraphQlTest
import org.springframework.graphql.test.tester.GraphQlTester

@GraphQlTest(AuthResolver::class)
class AuthResolverTest {

    @Autowired
    lateinit var graphQlTester: GraphQlTester

    @Test
    fun `signUp and login return tokens`() {
        graphQlTester.document("mutation { signUp(username:\"alice\", password:\"secret\") }")
            .execute()
            .path("signUp")
            .entity(String::class.java)
            .satisfies { token -> assertThat(token).isNotBlank() }

        graphQlTester.document("mutation { login(username:\"alice\", password:\"secret\") }")
            .execute()
            .path("login")
            .entity(String::class.java)
            .satisfies { token -> assertThat(token).isNotBlank() }
    }
}
