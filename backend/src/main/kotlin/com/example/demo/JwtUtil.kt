package com.example.demo

import io.jsonwebtoken.Jwts
import io.jsonwebtoken.SignatureAlgorithm
import io.jsonwebtoken.security.Keys
import java.util.Date

object JwtUtil {
    private val key = Keys.secretKeyFor(SignatureAlgorithm.HS256)
    private const val EXPIRATION_MS = 24 * 60 * 60 * 1000 // 1 day

    fun generateToken(username: String): String {
        val now = Date()
        val expiry = Date(now.time + EXPIRATION_MS)
        return Jwts.builder()
            .setSubject(username)
            .setIssuedAt(now)
            .setExpiration(expiry)
            .signWith(key)
            .compact()
    }

    fun validateToken(token: String): String? = try {
        Jwts.parserBuilder()
            .setSigningKey(key)
            .build()
            .parseClaimsJws(token)
            .body
            .subject
    } catch (ex: Exception) {
        null
    }
}
