package com.example.demo

import org.springframework.graphql.data.method.annotation.Argument
import org.springframework.graphql.data.method.annotation.MutationMapping
import org.springframework.stereotype.Controller

@Controller
class QuestionMutation(private val repository: QuestionRepository) {
    @MutationMapping
    fun createQuestion(
        @Argument prompt: String,
        @Argument topic: String,
        @Argument exampleAnswer: String?
    ): Question =
        repository.createQuestion(prompt, topic, exampleAnswer)
}
