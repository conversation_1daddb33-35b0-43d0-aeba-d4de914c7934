package com.example.demo

import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.multipart.MultipartFile
import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.StandardCopyOption
import java.util.UUID

@RestController
class AudioController {
    @PostMapping("/api/upload-audio")
    fun upload(@RequestParam("file") file: MultipartFile): Map<String, String> {
        val uploadDir = Path.of("uploads")
        Files.createDirectories(uploadDir)
        val filename = UUID.randomUUID().toString() + "-" + (file.originalFilename ?: "audio.webm")
        val target = uploadDir.resolve(filename)
        Files.copy(file.inputStream, target, StandardCopyOption.REPLACE_EXISTING)
        return mapOf("url" to "/uploads/$filename")
    }
}
