package com.example.demo

import org.jooq.DSLContext
import org.springframework.stereotype.Repository
import java.time.LocalDateTime
import java.util.UUID

@Repository
class QuestionRepository(private val dsl: DSLContext) {
    fun findAll(): List<Question> =
        dsl.select()
            .from("question")
            .fetch { record ->
                Question(
                    id = record.get("id", UUID::class.java),
                    prompt = record.get("prompt", String::class.java),
                    topic = record.get("topic", String::class.java),
                    exampleAnswer = record.get("example_answer", String::class.java),
                    createdAt = record.get("created_at", LocalDateTime::class.java)
                )
            }

    fun findPracticeQuestions(limit: Int = 10): List<Question> =
        dsl.select()
            .from("question")
            .limit(limit)
            .fetch { record ->
                Question(
                    id = record.get("id", UUID::class.java),
                    prompt = record.get("prompt", String::class.java),
                    topic = record.get("topic", String::class.java),
                    exampleAnswer = record.get("example_answer", String::class.java),
                    createdAt = record.get("created_at", LocalDateTime::class.java)
                )
            }
}
