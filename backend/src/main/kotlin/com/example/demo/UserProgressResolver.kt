package com.example.demo

import org.springframework.graphql.data.method.annotation.Argument
import org.springframework.graphql.data.method.annotation.QueryMapping
import org.springframework.stereotype.Controller
import java.util.UUID

@Controller
class UserProgressResolver(private val userAnswerRepository: UserAnswerRepository) {

    @QueryMapping
    fun userProgress(@Argument userId: UUID): UserProgress =
        userAnswerRepository.getUserProgress(userId)

    @QueryMapping
    fun userAnswerHistory(@Argument userId: UUID): List<AnswerHistoryEntry> =
        userAnswerRepository.findAnswerHistory(userId)
}
