package com.example.demo

import org.jooq.DSLContext
import org.jooq.impl.DSL
import org.springframework.stereotype.Repository
import java.time.LocalDateTime
import java.util.UUID

@Repository
class UserAnswerRepository(private val dsl: DSLContext) {
    fun saveAnswer(
        questionId: UUID,
        userId: UUID,
        answerText: String,
        rating: Int?,
        timeTakenSeconds: Int?,
        audioUrl: String?
    ): UserAnswer {
        val id = UUID.randomUUID()
        val now = LocalDateTime.now()

        dsl.insertInto(DSL.table("user_answer"))
            .set(DSL.field("id"), id)
            .set(DSL.field("user_id"), userId)
            .set(DSL.field("question_id"), questionId)
            .set(DSL.field("answer_text"), answerText)
            .set(DSL.field("audio_url"), audioUrl)
            .set(DSL.field("rating"), rating)
            .set(DSL.field("answered_at"), now)
            .set(DSL.field("time_taken_seconds"), timeTakenSeconds)
            .execute()

        return UserAnswer(
            id = id,
            userId = userId,
            questionId = questionId,
            answerText = answerText,
            audioUrl = audioUrl,
            rating = rating,
            answeredAt = now,
            timeTakenSeconds = timeTakenSeconds
        )
    }

    fun getUserProgress(userId: UUID): UserProgress {
        val total = dsl.selectCount()
            .from("user_answer")
            .where(DSL.field("user_id").eq(userId))
            .fetchOne(0, Int::class.java) ?: 0

        val avg = dsl.select(DSL.avg(DSL.field("rating", Int::class.java)))
            .from("user_answer")
            .where(DSL.field("user_id").eq(userId))
            .fetchOne(0, Double::class.java)

        return UserProgress(totalAnswers = total, averageRating = avg)
    }

    fun findAnswerHistory(userId: UUID, limit: Int = 20): List<AnswerHistoryEntry> =
        dsl.select(
                DSL.field("question.prompt", String::class.java),
                DSL.field("user_answer.rating", Int::class.java),
                DSL.field("user_answer.answered_at", LocalDateTime::class.java)
            )
            .from("user_answer")
            .join("question").on(DSL.field("user_answer.question_id").eq(DSL.field("question.id")))
            .where(DSL.field("user_answer.user_id").eq(userId))
            .orderBy(DSL.field("user_answer.answered_at").desc())
            .limit(limit)
            .fetch { record ->
                AnswerHistoryEntry(
                    questionPrompt = record.get(0, String::class.java)!!,
                    rating = record.get(1, Int::class.java),
                    answeredAt = record.get(2, LocalDateTime::class.java)!!
                )
            }
}
