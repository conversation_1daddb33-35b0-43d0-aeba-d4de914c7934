package com.example.demo

import org.springframework.graphql.data.method.annotation.Argument
import org.springframework.graphql.data.method.annotation.MutationMapping
import org.springframework.graphql.data.method.annotation.QueryMapping
import org.springframework.stereotype.Controller
import org.springframework.security.core.context.SecurityContextHolder

@Controller
class AuthResolver {

    private val users = mutableMapOf<String, String>()

    @MutationMapping
    fun signUp(@Argument username: String, @Argument password: String): String {
        users[username] = password
        return JwtUtil.generateToken(username)
    }

    @MutationMapping
    fun login(@Argument username: String, @Argument password: String): String {
        val stored = users[username]
        require(stored != null && stored == password) { "Invalid credentials" }
        return JwtUtil.generateToken(username)
    }

    @QueryMapping
    fun currentUser(): String? =
        SecurityContextHolder.getContext().authentication?.name
}
