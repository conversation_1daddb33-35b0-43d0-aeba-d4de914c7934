package com.example.demo

import org.springframework.graphql.data.method.annotation.Argument
import org.springframework.graphql.data.method.annotation.MutationMapping
import org.springframework.graphql.data.method.annotation.QueryMapping
import org.springframework.stereotype.Controller
import java.util.UUID

@Controller
class PracticeResolver(
    private val questionRepository: QuestionRepository,
    private val userAnswerRepository: UserAnswerRepository
) {

    @QueryMapping
    fun getPracticeQuestions(): List<Question> =
        questionRepository.findPracticeQuestions()

    @MutationMapping
    fun submitAnswer(
        @Argument questionId: UUID,
        @Argument userId: UUID,
        @Argument answerText: String,
        @Argument rating: Int?,
        @Argument timeTakenSeconds: Int?,
        @Argument audioUrl: String?
    ): UserAnswer =
        userAnswerRepository.saveAnswer(
            questionId = questionId,
            userId = userId,
            answerText = answerText,
            rating = rating,
            timeTakenSeconds = timeTakenSeconds,
            audioUrl = audioUrl
        )
}
