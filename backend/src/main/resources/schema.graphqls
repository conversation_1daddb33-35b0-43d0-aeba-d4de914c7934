type Question {
    id: ID!
    prompt: String!
    topic: String!
    exampleAnswer: String
    createdAt: String
}

type UserAnswer {
    id: ID!
    userId: ID!
    questionId: ID!
    answerText: String!
    audioUrl: String
    rating: Int
    answeredAt: String
    timeTakenSeconds: Int
}

type UserProgress {
    totalAnswers: Int!
    averageRating: Float
}

type AnswerHistoryEntry {
    questionPrompt: String!
    rating: Int
    answeredAt: String!
}

type Query {
    hello: String
    questions: [Question!]!
    getPracticeQuestions: [Question!]!
    userProgress(userId: ID!): UserProgress!
    userAnswerHistory(userId: ID!): [AnswerHistoryEntry!]!
    currentUser: String
}

type Mutation {
    submitAnswer(
        questionId: ID!
        userId: ID!
        answerText: String!
        rating: Int
        timeTakenSeconds: Int
        audioUrl: String
    ): UserAnswer
    login(username: String!, password: String!): String
    signUp(username: String!, password: String!): String
}
