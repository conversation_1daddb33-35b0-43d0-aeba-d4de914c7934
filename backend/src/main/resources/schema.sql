CREATE TABLE "user" (
  id UUID PRIMARY KEY,
  email VA<PERSON>HAR NOT NULL,
  password_hash VA<PERSON>HAR NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE question (
  id UUID PRIMARY KEY,
  prompt TEXT NOT NULL,
  topic VARCHAR NOT NULL,
  example_answer TEXT,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE user_answer (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES "user"(id),
  question_id UUID REFERENCES question(id),
  answer_text TEXT NOT NULL,
  audio_url VARCHAR,
  rating INT,
  answered_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  time_taken_seconds INT
);
