 English Speaking Practice Web App — Feature Requirements & Task Breakdown
1. User Stories & Feature Requirements
A. Daily Speaking Practice
Users receive a daily set (10–20) of speaking questions.

Each question shows:

The question prompt

The topic/type (e.g., Work, Travel, Daily Life)

An example answer for guidance

Users must answer within 1 minute per question.

After answering, users self-assess by rating their answer (1–5 stars).

B. User Progress & History
Users can review previous practice sessions, answers, and self-ratings.

Stats dashboard shows progress over time.

C. Question Management (Admin)
Admin can create, edit, delete, and categorize questions.

(Optional MVP: Start with a fixed seed of questions.)

D. User Authentication
Users sign up, log in, and manage their account securely.

E. Audio Answer Support (Optional)
Users can record and submit spoken answers (not just text).

2. Task Breakdown
A. System Setup
 Define database schema (User, Question, UserAnswer, Topic)

 Set up backend (Spring Boot + Kotlin, GraphQL, jOOQ, Java Virtual Threads)

 Set up frontend (Next.js, TypeScript, UI Library)

 Configure database (PostgreSQL)

 Set up version control and CI/CD

B. Backend Tasks
Auth
 Implement JWT-based signup/login

 Add user session management

Question Management
 Create CRUD APIs for questions (GraphQL mutations/queries)

 Add topic/type support

 Seed database with initial questions

Practice Session
 API to fetch daily questions per user

 API to submit answers, time taken, and self-rating

 Store user answers (including optional audio)

User Progress
 API to get user’s practice history

 API to get progress stats (e.g., average score, streaks)

Audio Support (Optional)
 Add endpoint for uploading/retrieving audio answers

 Integrate with cloud/local storage

C. Frontend Tasks
Auth
 Sign up / login UI

 Session management

Practice Session UI
 Show daily questions (topic, prompt, example)

 Countdown timer per question

 Answer input (text, optional audio recorder)

 Self-rating (star selector)

 Submit to backend

User Progress UI
 Dashboard of scores, answers, streaks

 Review previous sessions

Admin UI (Optional)
 Manage questions/topics

Audio Support (Optional)
 Integrate browser mic for recording answers

 Playback submitted answers

D. DevOps/Deployment
 Deploy backend API (cloud/VPS)

 Deploy frontend (Vercel/Netlify)

 Configure environment variables/secrets

 Set up monitoring/logging

E. Testing & QA
 Unit/integration tests for backend

 UI/functional tests for frontend

 User acceptance testing

3. Database Schema (Quick Reference)
sql
Copy
Edit
User (
  id UUID PK,
  email VARCHAR,
  password_hash VARCHAR,
  created_at TIMESTAMP
)

Question (
  id UUID PK,
  prompt TEXT,
  topic VARCHAR,
  example_answer TEXT,
  created_at TIMESTAMP
)

UserAnswer (
  id UUID PK,
  user_id UUID FK,
  question_id UUID FK,
  answer_text TEXT,
  audio_url VARCHAR, -- optional
  rating INT, -- 1-5
  answered_at TIMESTAMP,
  time_taken_seconds INT
)
4. Milestones (Example)
Setup & Auth: Project skeleton, DB, auth flow

Question Flow: Question CRUD, daily practice APIs, practice UI

Self-Rating & History: Answer/rating API, progress UI

Audio (Optional): Audio recording/upload, audio storage

Admin & Polish: Admin panel, UI/UX polish

Deployment: CI/CD, cloud deployment, production ready
