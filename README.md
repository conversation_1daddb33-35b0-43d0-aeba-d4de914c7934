# English Practice App

A. Daily Speaking Practice
Users receive a daily set (10–20) of speaking questions.

Each question shows:

The question prompt

The topic/type (e.g., Work, Travel, Daily Life)

An example answer for guidance

Users must answer within 1 minute per question.

After answering, users self-assess by rating their answer (1–5 stars).

B. User Progress & History
Users can review previous practice sessions, answers, and self-ratings.

Stats dashboard shows progress over time.
Navigate to `/dashboard` in the frontend to view a summary of your answers and ratings.

C. Question Management (Admin)
Admin can create, edit, delete, and categorize questions.

(Optional MVP: Start with a fixed seed of questions.)

D. User Authentication
Users sign up, log in, and manage their account securely.

E. Audio Answer Support (Optional)
Users can record and submit spoken answers (not just text).

## Backend

The backend is located in the `backend/` directory. It contains a minimal Spring Boot setup with a simple GraphQL query.

Build the backend with:

```bash
./gradlew build
```

## Database & jOOQ Integration

The SQL schema for the core tables is stored in `backend/src/main/resources/schema.sql`. Spring Boot configures the `DataSource` via `application.yml`, and `DatabaseConfig` provides a jOOQ `DSLContext` bean. `QuestionRepository` shows how to query the tables using jOOQ.


## Frontend

The frontend is in the `frontend/` directory and was initialized manually. Typical Next.js commands are available via `npm` or `yarn`.

Run the development server with:

```bash
npm run dev
```

## Project Idea

See the `readname` file for a high-level overview of desired features for the English speaking practice app.

## Running with Docker

Use `docker-compose` to start the database, backend and frontend services:

```bash
docker-compose up --build
```

The frontend will be available at [http://localhost:3000](http://localhost:3000) and the backend GraphQL endpoint at [http://localhost:8080/graphql](http://localhost:8080/graphql).

Use `Ctrl+C` to stop the services. The PostgreSQL data is persisted in the `db-data` volume.
